#pragma once

#include "pch.h"
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>

// Forward declarations
class Overlay;

class MenuInputHandler {
private:
    std::thread inputThread;
    std::atomic<bool> shouldRun{true};
    std::atomic<bool> menuVisible{false};
    std::atomic<bool> menuStateChanged{false};
    std::mutex stateMutex;

    // Reference to the main overlay for shared resources
    Overlay* overlay;

    // Input thread function
    void InputThreadFunction();

    // Handle menu input and state changes
    void HandleMenuInput();

public:
    MenuInputHandler(Overlay* overlayRef);
    ~MenuInputHandler();

    // Start the input thread
    void StartInputThread();

    // Stop the input thread
    void StopInputThread();

    // Check if menu is visible
    bool IsMenuVisible() const { return menuVisible.load(); }

    // Set menu visibility (thread-safe)
    void SetMenuVisible(bool visible);

    // Get menu visibility for external access
    bool GetMenuVisible() const { return menuVisible.load(); }

    // Check if menu state has changed (and reset the flag)
    bool HasMenuStateChanged();
};
