#include "pch.h"
#include "menu_renderer.hpp"
#include "window.hpp"
#include "../cheat/animations/ui/menu_animations.hpp"

MenuInputHandler::MenuInputHandler(Overlay* overlayRef) : overlay(overlayRef) {
    // Constructor - initialize but don't start thread yet
}

MenuInputHandler::~MenuInputHandler() {
    StopInputThread();
}

void MenuInputHandler::StartInputThread() {
    if (!inputThread.joinable()) {
        shouldRun = true;
        inputThread = std::thread(&MenuInputHandler::InputThreadFunction, this);
    }
}

void MenuInputHandler::StopInputThread() {
    shouldRun = false;
    if (inputThread.joinable()) {
        inputThread.join();
    }
}

void MenuInputHandler::SetMenuVisible(bool visible) {
    menuVisible = visible;
    menuStateChanged = true;
}

void MenuInputHandler::InputThreadFunction() {
    // Wait for overlay to be properly initialized
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // Set high priority for this thread
    SetThreadPriority(GetCurrentThread(), THREAD_PRIORITY_ABOVE_NORMAL);

    while (shouldRun) {
        // Handle input and menu state
        HandleMenuInput();

        // Run at higher FPS when menu is visible for ultra-smooth input
        if (menuVisible) {
            std::this_thread::sleep_for(std::chrono::milliseconds(8)); // ~120 FPS
        } else {
            std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
        }
    }
}

void MenuInputHandler::HandleMenuInput() {
    // Handle HOME key for menu toggle
    if (GetAsyncKeyState(VK_HOME) & 1) {
        // Check if menu toggle is allowed (no animations running)
        if (MenuAnimations::CanToggleMenu()) {
            bool oldMenuVisible = menuVisible;
            menuVisible = !menuVisible;
            menuStateChanged = true;

            // Trigger animations when menu state changes
            if (!oldMenuVisible && menuVisible) {
                // Menu just opened
                MenuAnimations::StartMenuOpenAnimation(0.5f);
            }
            else if (oldMenuVisible && !menuVisible) {
                // Menu just closed
                MenuAnimations::StartMenuCloseAnimation(0.3f);
            }

            // Update window styles for input handling
            if (overlay) {
                if (menuVisible) {
                    SetWindowLong(overlay->overlay, GWL_EXSTYLE, WS_EX_TOOLWINDOW | WS_EX_TRANSPARENT);
                }
                else {
                    SetWindowLong(overlay->overlay, GWL_EXSTYLE, WS_EX_TOOLWINDOW | WS_EX_TRANSPARENT | WS_EX_TOPMOST | WS_EX_LAYERED);
                }
            }
        }
    }
}

bool MenuInputHandler::HasMenuStateChanged() {
    bool expected = true;
    return menuStateChanged.compare_exchange_strong(expected, false);
}
